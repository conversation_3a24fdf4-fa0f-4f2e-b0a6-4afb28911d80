from fastapi import APIRouter

from app.api.routes import items, login, private, users, utils, integration_test, documents, embedding, search, llm, conversations, summaries
from app.core.config import settings

api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)
api_router.include_router(documents.router)
api_router.include_router(search.router)
api_router.include_router(embedding.router, prefix="/embedding", tags=["embedding"])
api_router.include_router(llm.router, prefix="/llm", tags=["llm"])


if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)
    api_router.include_router(integration_test.router, prefix="/integration", tags=["integration"])
api_router.include_router(conversations.router, prefix="/conversations", tags=["conversations"])
api_router.include_router(summaries.router, prefix="/summaries", tags=["summaries"])
